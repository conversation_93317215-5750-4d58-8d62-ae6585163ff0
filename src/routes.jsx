import { Routes, Route, Navigate } from 'react-router-dom'
import { Icon, Status } from '@bitcine/cinesend-theme'
import Dashboard from './pages/dashboard'
import Login from './pages/login'
import Cycle from './pages/cycle'
import Issues from './pages/issues'
import Metadata from './pages/metadata'
import Account from './pages/account'
import Airlines from './pages/airlines'
import Airline from './pages/airline'
import Cycles from './pages/cycles'
import Validators from './pages/validators'
import Schemas from './pages/schemas'
import Schema from './pages/schema'
import Categories from './pages/metadata/categories'
import Category from './pages/category'
import Users from './pages/users'
import User from './pages/user'
import Images from './pages/images'
import Organizations from './pages/organizations'
import Organization from './pages/organization'
import ActivityLogs from './pages/activity_logs'
import Lists from './pages/lists'
import List from './pages/list'
import Ads from './pages/ads'
import AirlineCycleWrapper from './components/airline_cycle_wrapper'
import LandingPage from './pages/landing_page'

const MessagePage = ({ message, iconName }) => {
  return (
    <div className='flex h-screen items-center justify-center text-xl'>
      <div className='flex-col space-y-4'>
        <div className='flex justify-center'>
          <Icon icon={iconName} className='text-6xl' />
        </div>
        <div>{message}</div>
      </div>
    </div>
  )
}
const ComingSoon = () => {
  return <MessagePage message='Coming Soon.' iconName='engineering' />
}

export const RoutesList = ({ user, isUserLoading, organization }) => {
  // If we are still checking the auth user status, return a pending spinner.
  if (isUserLoading) {
    return <Status pending className='h-screen' />
  }

  if (!user) {
    return (
      <Routes>
        <Route path='/login' element={<Login organization={organization} />} />
        <Route path='*' element={<Navigate to='/login' replace={true} />} />
      </Routes>
    )
  }

  return (
    <Routes>
      <Route element={<AirlineCycleWrapper />}>
        <Route path='/' element={<LandingPage />} />
        <Route
          path='/airlines/:airlineID'
          element={<MessagePage message='Choose a cycle from the sidebar.' iconName='date_range' />}
        />
        <Route path='/airlines/:airlineID/cycles/:cycleID/*'>
          <Route path='dashboard' element={<Dashboard />} />
          <Route path='issues' element={<Issues />} />
          <Route path='details' element={<Cycle />} />
          <Route path='categories' element={<Categories />} />
          <Route path='categories/:categoryID/*' element={<Category />} />
          <Route path='images/*' element={<Images />} />
          <Route path='lists/' element={<Lists />} />
          <Route path='lists/:listID' element={<List />} />
          <Route path='ads/*' element={<Ads />} />
          <Route path='metadata/*' element={<Metadata />} />
          <Route path='*' element={<Navigate to='dashboard' replace={true} />} />
        </Route>
        <Route path='/settings/*'>
          {user.has_unrestricted_edit_role ? (
            <>
              <Route path='airlines' element={<Airlines />} />
              <Route path='airlines/:airlineID/*' element={<Airline />} />
              <Route path='cycles' element={<Cycles />} />
              <Route path='schemas' element={<Schemas />} />
              <Route path='schemas/:schemaID/*' element={<Schema />} />
              <Route path='validators' element={<Validators />} />
              <Route path='validators/*' element={<Validators />} />
              <Route path='account/*' element={<Account />} />
              <Route path='users' element={<Users />} />
              <Route path='users/:userID/*' element={<User />} />
              <Route path='organizations' element={<Organizations />} />
              <Route path='organizations/:orgID' element={<Organization />} />
              <Route path='activity-logs' element={<ActivityLogs />} />
              <Route path='*' element={<Navigate to='airlines' replace={true} />} />
            </>
          ) : (
            <>
              <Route path='account/*' element={<Account />} />
              {/* <Route path='*' element={<Navigate to='/' replace />}  */}
            </>
          )}
        </Route>
        <Route path='/coming-soon' element={<ComingSoon />} />
        {/* <Route path="*" element={<Navigate to="/airlines" replace={true} />} /> */}
      </Route>
      <Route path='*' element={<Navigate to='/' replace />} />
    </Routes>
  )
}
