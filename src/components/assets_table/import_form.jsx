import { useState } from 'react'
import { Modal, FileUpload, Status } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useQueryClient } from '@tanstack/react-query'

const ImportForm = ({ isOpen, onClose, cycleID, assetType, queryKey }) => {
  const { updateModel } = useStore()
  const [errors, setErrors] = useState('')
  const [processing, setProcessing] = useState(false)
  const queryClient = useQueryClient()
  const processImport = (id) => {
    updateModel.mutate(
      {
        endpoint: `/api/imports`,
        id: `${id}/process`,
        data: {
          cycle_id: cycleID,
          asset_type: assetType,
        },
      },
      {
        onSuccess: (res) => {
          console.error('Upload res:', res)
          if (res.data?.data?.errors?.length === 0) {
            onClose()
          }
          setErrors(res.data?.data?.errors)
          queryClient.invalidateQueries([queryKey])
        },
        onSettled: () => setProcessing(false),
        onError: (error) => {
          console.error('Upload error:', error)
          setErrors(error.response?.data?.data?.errors || 'Error uploading file')
        },
      }
    )
  }
  return (
    <Modal isOpen={isOpen} onClose={onClose} header='Upload Import File'>
      <div className='text-sm import-form'>
        <Status pending={processing}>
          <FileUpload
            className={'h-40'}
            includeRemoveButton={false}
            upload={{
              message: 'Drop CSV here',
              accept: 'text/csv',
              icon: 'document_scanner',
              apiURL: `${import.meta.env.VITE_API_URL}/api/imports`,
              onComplete: (file, destinationUrl, importDetails) => {
                setProcessing(true)
                processImport(importDetails?.data?.id)
              },
            }}
            button={{
              text: 'Upload',
            }}
          />
        </Status>
        {errors && (
          <div className='pt-4 space-y-4'>
            {Array.isArray(errors) ? (
              errors.map((line, index) => (
                <div key={index} className='bg-error-200 p-3 rounded-md shadow'>
                  {line.message}
                </div>
              ))
            ) : (
              <div className='bg-error-200 p-3 rounded-md shadow'>{errors}</div>
            )}
          </div>
        )}
      </div>
    </Modal>
  )
}

export default ImportForm
