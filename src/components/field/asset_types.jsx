import { Select, Message } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import useUtilities from '../../hooks/use_utilities'

const AssetTypes = ({ field, saveField }) => {
  const defaultAssetTypes = field.asset_types || []
  const [assetTypes, setAssetTypes] = useState(defaultAssetTypes)
  const [error, setError] = useState(null)
  const { assetTypes: availableAssetTypes } = useUtilities()
  const assetTypeOptions = availableAssetTypes?.map((opt) => ({ ...opt, label: opt.plural_label }))

  const update = (data) => {
    // Validate that at least one asset type is selected
    if (data.length === 0) {
      setError('At least one asset type must be selected.')
      // Don't update the state - retain the last selection
      return
    }

    // Clear error if validation passes
    setError(null)
    setAssetTypes(data)
    saveField({ asset_types: data })
  }

  return (
    <div>
      <Select
        options={assetTypeOptions}
        isMulti
        value={assetTypeOptions?.filter((opt) => assetTypes.includes(opt.value))}
        label={'Specified Asset Types'}
        description={'At least one asset type must be selected.'}
        error={!!error}
        onChange={(opt) => update(opt.map((opt) => opt.value))}
      />
      {error && (
        <Message error={true} small={true}>
          {error}
        </Message>
      )}
    </div>
  )
}

export default AssetTypes
