import { Input } from '@bitcine/cinesend-theme'
import { useState } from 'react'

const InputKey = ({ field, saveField }) => {
  const [inputKey, setInputKey] = useState(field.input_key ?? '')
  return (
    <Input
      label={'Input Key'}
      value={inputKey}
      onChange={(e) => setInputKey(e.target.value)}
      onBlur={() => {
        saveField({ input_key: inputKey })
      }}
    />
  )
}

export default InputKey
