import { Input, Select } from '@bitcine/cinesend-theme'
import React, { useEffect, useState } from 'react'
import useStore from '../../hooks/use_store'

const ValidatorParams = ({ name, data, fieldId, refreshData }) => {
  const [value, setValue] = useState(data.parameters.value)
  const { createModel } = useStore()

  useEffect(() => {
    setValue(data.parameters?.value)
  }, [data.parameters?.value])

  const saveParamsValue = (value) => {
    createModel.mutate({
      endpoint: 'validator-fields',
      data: {
        validator_id: data.id,
        field_id: fieldId,
        severity: data.severity,
        enabled: data.active,
        parameters: {
          options: data.parameters.options,
          value: value,
        },
      },
    }, {
      onSuccess: refreshData
    })
  }

  // for list return dropdown
  if (data.parameters.options) {
    return (
      <Select
        id={name}
        options={data.parameters.options}
        value={data.parameters.options.find((item) => item.value == value)}
        onChange={(input) => {
          setValue(input.value)
          saveParamsValue(input.value)
        }}
      />
    )
  }

  return (
    <Input name={name} value={value} onChange={(e) => setValue(e.target.value)} onBlur={() => saveParamsValue(value)} />
  )
}

export default ValidatorParams
