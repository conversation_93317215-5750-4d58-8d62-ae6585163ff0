import { Status } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import Breadcrumbs from '../layouts/breadcrumbs'
import Identifier from './identifier'
import Type from './type'
import Validators from './validators'
import SelectCollection from './select_collection'
import AssetTypes from './asset_types'
import useStore from '../../hooks/use_store'
import IsLocalizable from './is_localizable'
import LocalizedLabels from './localized_labels'
import IsExternalApiValueSource from './is_external_api_value_source'
import IsExternalApiKeyField from './is_external_api_key_field'
import IsSizeField from './is_size_field'
import InputKey from './input_key'
import OutputKey from './output_key'

const Field = ({ schema, cycleID }) => {
  const fieldID = useParams().fieldID
  const { data: field, isLoading, error, isError } = useQuery(['fields/', fieldID])
  const { updateModel } = useStore()
  const saveField = (data) => {
    updateModel.mutate({
      endpoint: `fields`,
      id: field.id,
      data,
    })
  }
  return (
    <Status pending={isLoading} error={isError} errorMessage={error?.message}>
      {field ? (
        <div className='space-y-4'>
          <Breadcrumbs
            className='my-2 border-b pb-4'
            breadcrumbs={[
              { text: 'Schemas', to: `/settings/schemas` },
              { text: schema?.name, to: `/settings/schemas/${schema?.id}` },
              { text: 'Fields', to: `/settings/schemas/${schema?.id}/fields` },
              { text: field?.name },
            ]}
          />
          <Type field={field} />
          {field?.field_type === 'dropdown' ? (
            <SelectCollection field={field} schema={schema} saveField={saveField} />
          ) : null}
          <Identifier field={field} saveField={saveField} />
          <LocalizedLabels cycleID={cycleID} field={field} airlineID={schema?.airline_id} />
          <InputKey field={field} saveField={saveField} />
          <OutputKey field={field} saveField={saveField} />
          <IsLocalizable field={field} saveField={saveField} />
          <IsExternalApiKeyField field={field} saveField={saveField} />
          <IsExternalApiValueSource field={field} saveField={saveField} />
          <IsSizeField field={field} saveField={saveField} />
          <AssetTypes field={field} saveField={saveField} />
        </div>
      ) : null}

      <Validators fieldId={fieldID} type={field?.field_type} />
    </Status>
  )
}

export default Field
