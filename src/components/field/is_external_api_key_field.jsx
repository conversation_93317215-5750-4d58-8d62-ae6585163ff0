import { Toggle } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'

const IsExternalApiKeyField = ({ field, saveField }) => {
  const [isExternalApiKeyField, setIsExternalApiKeyField] = useState(field.is_external_api_key_field)
  return (
    <Toggle
      className='space-x-4'
      checked={isExternalApiKeyField}
      label='Use as key field for Viasat API mapping'
      onChange={() => {
        setIsExternalApiKeyField(!isExternalApiKeyField)
        saveField({ is_external_api_key_field: !isExternalApiKeyField })
      }}
    />
  )
}

export default IsExternalApiKeyField
