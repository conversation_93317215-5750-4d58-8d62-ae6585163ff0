import { Button, ButtonDropdown, Table, Toggle } from '@bitcine/cinesend-theme'
import React from 'react'
import useStore from '../../hooks/use_store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import ValidatorParams from './validator_params'

const Validators = ({ fieldId, type }) => {
  const { createModel, updateModel } = useStore()
  const queryClient = useQueryClient();
  const queryKey = `validator-fields?type=${type}&field_id=${fieldId}`;
  const { isLoading, error, data: validators } = useQuery([queryKey])

  const formatParameters = (data) => {
    if (!data.parameters || typeof data.parameters != 'object' || Object.keys(data.parameters).length === 0) {
      return ''
    }

    return <ValidatorParams key={`${data.name}-${data.id}`} name={data.name} data={data} fieldId={fieldId} refreshData={refreshData} />
  }

  const refreshData = () => {
    queryClient.invalidateQueries([queryKey])
  }

  const onStatusChange = (item) => {
    createModel.mutate({
      endpoint: 'validator-fields',
      data: {
        validator_id: item.id,
        field_id: fieldId,
        severity: item.severity,
        enabled: item.active,
        parameters: item.parameters,
      },
    }, {
      onSuccess: () => refreshData()
    })
  }

  const severityDropdown = (data) => {
    const content =
      validators?.severity_levels?.map((item) => ({
        text: item.label,
        onClick: () => {
          onStatusChange({ ...data, severity: item.value })
        },
      })) || []

    return (
      <ButtonDropdown
        button={{
          text: data.severity || 'N/A',
          link: true,
        }}
        dropdown={{
          content: content,
        }}
      />
    )
  }

  const onValidatorReset = (item) => {
    createModel.mutate({
      endpoint: 'validator-fields/reset',
      data: {
        validator_id: item.id,
        field_id: fieldId,
      }
    }, {
      onSuccess: () => refreshData()
    });
  }

  if (!validators?.severity_levels) {
    return <div>Loading...</div>
  }

  return (
    <div className='pt-5 '>
      <p>Field Validators:</p>
      <Table
        // if validator has a list of params we need it to be visible
        className='[&>tbody>tr]:!overflow-visible [&>tbody>tr>td]:!overflow-visible'
        status={{
          pending: isLoading,
          error: error,
          errorMessage: error?.message,
        }}
        widths={[100, 120, 180, 'auto', 100, 50]}
        header={{
          columns: [
            { text: 'Active', key: 'active' },
            { text: 'Validator', key: 'validator' },
            { text: 'Value', key: 'parameters' },
            { text: 'Description', key: 'description' },
            { text: 'Severity', key: 'severity' },
            { text: 'Reset', key: 'reset' }
          ],
          sorting: {},
          filters: {},
        }}
        body={{
          data: validators?.data,
          row: {
            spaced: true,
            compact: true,
            truncate: false,
            render: [
              (data) => (
                <Toggle
                  id={data.id}
                  key={data.id}
                  onChange={() => {
                    onStatusChange({ ...data, active: !data.active })
                  }}
                  checked={data.active}
                />
              ),
              (data) => data.name,
              (data) => formatParameters(data),
              (data) => data.description,
              (data) => severityDropdown(data),
              (data) => <Button size='small' type='neutral' secondary={true} icon={'refresh'} onClick={() => onValidatorReset(data)} />
            ],
          },
          empty: {
            title: 'No validators found!',
            text: 'Create a new validator to begin.',
            icon: 'info',
          },
        }}
      />
    </div>
  )
}

export default Validators
