import { Input } from '@bitcine/cinesend-theme'
import { useState } from 'react'

const OutputKey = ({ field, saveField }) => {
  const [outputKey, setOutputKey] = useState(field.output_key ?? '')
  return (
    <Input
      label={'Output Key'}
      value={outputKey}
      onChange={(e) => setOutputKey(e.target.value)}
      onBlur={() => {
        saveField({ output_key: outputKey })
      }}
    />
  )
}

export default OutputKey
