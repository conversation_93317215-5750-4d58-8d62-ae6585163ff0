import useStore from '../../hooks/use_store'
import ImagesTable from '../images_table'
import { Status } from '@bitcine/cinesend-theme/dist'

export default function Select({ assetID, field, onNewImageSelected }) {
  const { updateModel } = useStore()
  const saveImage = (assetImage) => {
    updateModel.mutate(
      {
        endpoint: '/asset-images',
        id: assetImage.id,
        data: {
          asset_id: assetID,
          field_id: field.id,
        },
      },
      {
        onSuccess: async () => {
          onNewImageSelected(assetImage)
        },
      }
    )
  }
  return (
    <Status pending={updateModel.isLoading}>
      <ImagesTable type='unmatched' onClick={(assetImage) => saveImage(assetImage)} />
    </Status>
  )
}
