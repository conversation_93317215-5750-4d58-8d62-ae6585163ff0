import { ImageUpload } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'
import { useParams } from 'react-router-dom'

export default function Upload({ assetID, field, assetImage, onNewImageSelected }) {
  const { airlineID, cycleID } = useParams()
  const { updateModel, createModel } = useStore()

  // Once an image is picked from the file selector, we either create or update the asset image.
  const startImageUpload = (file) => {
    const data = {
      asset_id: assetID,
      airline_id: airlineID,
      cycle_id: cycleID,
      field_id: field.id,
      size: file.size,
      file_name: file.name,
      mime: file.type,
    }
    if (assetImage) {
      updateModel.mutate(
        {
          endpoint: '/asset-images',
          id: assetImage.id,
          data,
        },
        {
          onSuccess: (res) => uploadToS3(res.data.signed_upload_url, file, res.data.asset_image),
        }
      )
      return
    }
    createModel.mutate(
      {
        endpoint: '/asset-images',
        data,
      },
      {
        onSuccess: (res) => uploadToS3(res.data.signed_upload_url, file, res.data.asset_image),
      }
    )
  }

  const uploadToS3 = (signedUrl, file, assetImage) => {
    // axios adds unnecessary headers
    fetch(signedUrl, {
      body: file,
      method: 'PUT',
      mode: 'cors',
      headers: {
        'Content-Type': file.type,
      },
    })
      .then((res) => {
        onNewImageSelected(assetImage)
      })
      .catch((err) => console.log(err))
  }

  return (
    <ImageUpload
      className={'h-48 w-full'}
      icon={'image'}
      button={{
        text: `Upload ${field.name}`,
      }}
      upload={{
        accept: {
          'image/*': ['.jpg'],
        },
        message: 'Drag and drop your image or click here to upload. Accepted file type: .jpg',
        onFileSelected: (file) => startImageUpload(file),
        onRemove: () => {},
      }}
    />
  )
}
