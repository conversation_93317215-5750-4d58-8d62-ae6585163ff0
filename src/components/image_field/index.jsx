import { useEffect, useState } from 'react'
import { Button, Input, Message } from '@bitcine/cinesend-theme/dist'
import ImageModal from './image_modal'
import { humanFileSize } from '../../helpers/human_file_size'
import isEmpty from '../../helpers/is_empty'

export default function ImageField({ assetID, field, value, message, saveField }) {
  const label = field.name
  const [showImageModal, setShowImageModal] = useState(false)
  const [fileName, setFileName] = useState(value ? value : '')

  const assetImage = field.asset_image
  const missingAssetImage = assetImage && !assetImage.id
  const mismatchedAssetFileName = assetImage && assetImage.file_name !== fileName

  useEffect(() => {
    if (value !== fileName && assetImage) {
      setFileName(value)
    }
  }, [value, fileName, setFileName, assetImage])

  const saveImageField = (newValue) => {
    // If field already has a value, don't overwrite it, it was set in csv (probably) to validate
    // that the correct asset is being manually selected on this form.
    // If its currently empty though, we should set a value so it has something.
    if (!isEmpty(fileName)) {
      return
    }

    saveField(newValue ?? '')
  }
  
  return (
    <div className='flex flex-col space-y-1'>
      <div className='flex items-end space-x-2'>
        <Input
          label={label}
          error={!!message}
          placeholder={'Enter a file name to match automatically to bulk uploaded images'}
          value={fileName ?? ''}
          disabled={!!assetImage}
          onChange={(e) => setFileName(e.target.value)}
          onBlur={(e) => saveField(e.target.value)} // use standard `saveField` here so value can be manually edited
        />
        {assetImage && mismatchedAssetFileName ? (
          <Input label={'Uploaded file name'} disabled value={assetImage.file_name ?? ''} />
        ) : null}
        {assetImage ? (
          <Input className='max-w-24' label={'File size'} disabled value={humanFileSize(assetImage.size)} />
        ) : null}
        <Button className='min-w-40' secondary onClick={() => setShowImageModal(true)}>
          {assetImage ? 'Manage image' : 'Select image'}
        </Button>
        {showImageModal ? (
          <ImageModal
            onClose={() => setShowImageModal(false)}
            assetImage={assetImage}
            assetID={assetID}
            field={field}
            saveField={saveImageField}
          />
        ) : null}
      </div>
      {message && (
        <Message error={true} small={true}>
          {message}
        </Message>
      )}
      {missingAssetImage && (
        <Message error={true} small={true}>
          No image found for this asset. Please upload an image.
        </Message>
      )}
      {mismatchedAssetFileName && (
        <Message error={true} small={true}>
          The file name does not match the uploaded image file name. Please check the file name.
        </Message>
      )}
    </div>
  )
}
