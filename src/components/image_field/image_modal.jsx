import { Modal } from '@bitcine/cinesend-theme/dist'
import Upload from './image_upload'
import Tabs from '../layouts/tabs'
import { useEffect, useState } from 'react'
import Select from './image_select'
import AttachedImage from './attached_image'
import { useQueryClient, useIsFetching } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'

export default function ImageModal({ assetID, assetImage, field, onClose, saveField }) {
  const [selectedTab, setSelectedTab] = useState('select')
  const [previewImage, setPreviewImage] = useState(assetImage)
  const params = useParams()
  const queryKeys = [`field-values?assetID=${assetID}&cycleID=${params.cycleID}`]
  const queryClient = useQueryClient()
  const isFetching = useIsFetching({ queryKey: queryKeys })

  const refreshPreview = (image) => {
    const newImage = !!image ? { ...image } : null
    setPreviewImage(newImage)
  }

  const onNewImageSelected = (image = null) => {
    saveField(image?.file_name)
    refreshPreview(image)
    queryClient.invalidateQueries([`fields`])
    queryClient.invalidateQueries([`field-values`])
  }

  useEffect(() => {
    if (previewImage?.file_name !== assetImage?.file_name) {
      queryClient.invalidateQueries(queryKeys)
    }
  }, [previewImage])

  return (
    <Modal header={`Manage ${field.name}`} className='w-2/3' onClose={onClose} pending={isFetching}>
      {previewImage ? (
        <AttachedImage
          assetID={assetID}
          field={field}
          assetImage={previewImage}
          onNewImageSelected={onNewImageSelected}
        />
      ) : (
        <div className='space-y-4'>
          <Tabs
            wide
            list={[
              {
                name: 'Select from Uploads',
                selected: selectedTab === 'select',
                onClick: () => setSelectedTab('select'),
              },
              {
                name: 'Upload',
                selected: selectedTab === 'upload',
                onClick: () => setSelectedTab('upload'),
              },
            ]}
          />
          {selectedTab === 'select' ? (
            <Select assetID={assetID} field={field} onNewImageSelected={onNewImageSelected} />
          ) : selectedTab === 'upload' ? (
            <Upload assetImage={assetImage} assetID={assetID} field={field} onNewImageSelected={onNewImageSelected} />
          ) : null}
        </div>
      )}
    </Modal>
  )
}
