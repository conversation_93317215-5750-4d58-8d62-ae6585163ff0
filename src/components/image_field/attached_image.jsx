import { Button, Image, Status } from '@bitcine/cinesend-theme/dist'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { humanFileSize } from '../../helpers/human_file_size'
import useStore from '../../hooks/use_store'
import { useParams } from 'react-router-dom'

export default function AttachedImage({ assetImage, onNewImageSelected }) {
  // First try and get an existing asset image...
  const { data, isLoading } = useQuery([`/asset-images/${assetImage.id}`], {
    retry: false,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  })
 
  const { deleteModel, updateModel } = useStore()
  const queryClient = useQueryClient()

  const deleteImage = () => {
    deleteModel.mutate(
      {
        endpoint: 'asset-images',
        id: assetImage.id,
      },
      {
        onSuccess: () => onNewImageSelected(),
      }
    )
  }

  const unmatchImage = () => {
    const assetImageID = assetImage.id
    updateModel.mutate(
      {
        endpoint: '/asset-images',
        id: assetImageID,
        data: {
          asset_id: null,
          field_id: null,
          status: 'uploaded',
        },
      },
      {
        onSuccess: () => {
          console.log('Image unlinked successfully, invalidating queries...')
          queryClient.invalidateQueries([`field-values`])

          onNewImageSelected()
        },
      }
    )
  }

  return (
    <Status pending={isLoading || updateModel.isLoading}>
      <div className={'flex items-start justify-center space-x-2 h-full'}>
        <div className='max-w-xs h-full space-y-2'>
          <Image url={data?.asset_image?.signed_url} />
        </div>
        <div className='space-y-2 justify-between flex-col'>
          <AssetImageDetails assetImage={assetImage} />
          <Button icon={'cancel'} type={'warning'} size='small' className='w-full' secondary onClick={unmatchImage}>
            Unmatch Image
          </Button>
          <Button
            icon={'delete_forever'}
            type={'error'}
            size='small'
            className='w-full'
            secondary
            onClick={deleteImage}
          >
            Delete Image
          </Button>
        </div>
      </div>
    </Status>
  )
}

const AssetImageDetails = ({ assetImage }) => {
  // show file and file size
  const fileName = assetImage.file_name
  const fileSize = humanFileSize(assetImage.size)

  return (
    <div className='flex-col p-4 rounded bg-primary-100'>
      <div className='flex justify-center'>File name: {fileName}</div>
      {fileSize ? <div className='flex justify-center'>File size: {fileSize}</div> : null}
    </div>
  )
}
