import { useLocation, useNavigate } from 'react-router-dom'

const Tabs = ({ list, wide = false, className = null }) => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname
  return (
    <div
      className={`${className ?? ''}
      flex items-center border-b mt-2
    `}
    >
      {list.map((tab, index) => {
        const hasPathnameMatch = tab.to
          ? tab.to.startsWith('/')
            ? pathname === tab.to
            : pathname.split('/').includes(tab.to)
          : false
        const isActive = tab.selected || hasPathnameMatch
        const isVisible = tab.visible ?? true
        if (!isVisible) {
          return null
        }
        return (
          <div
            key={index}
            onClick={() => (typeof tab.onClick === 'function' ? tab.onClick() : navigate(tab.to))}
            className={`capitalize font-semibold py-4 border-b-4 text-base font-subheader leading-4 flex items-center justify-center
          ${wide ? `w-full ${isActive ? 'bg-primary-100' : 'hover:bg-primary-100'}` : `w-auto`}
          ${
            isActive
              ? 'text-primary-500 border-primary-500'
              : 'cursor-pointer border-white text-primary-800 hover:border-primary-500 hover:text-primary-500'
          }`}
          >
            <span className='h-6 flex items-center px-2'>{tab.name.replace('-', ' ')}</span>
          </div>
        )
      })}
    </div>
  )
}

export default Tabs
