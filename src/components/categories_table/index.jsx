import { useQuery, useQueryClient } from '@tanstack/react-query'
import { ButtonDropdown, Table, Tag } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useNavigate, useParams } from 'react-router-dom'
import useTableFilters from '../../hooks/use_table_filters'
import Tabs from '../layouts/tabs'
import { useState } from 'react'

const Categories = ({ queryKey }) => {
  const params = useParams()
  const { deleteModel } = useStore()
  const queryClient = useQueryClient()
  const [selectedTab, setSelectedTab] = useState('categories')
  const { data: airline, isLoading: isAirlineLoading, error: airlineError } = useQuery(['airlines/', params.airlineID])
  const langID = airline?.languages?.[0]?.id || null

  const { queryString, searching, sorting, filtering } = useTableFilters({
    filteringOptions: null,
    enableSearch: true,
    queryExists: true,
  })

  const queryWithTypeParam = `${queryString}&type=${selectedTab}`

  const { data: categories, isLoading, error } = useQuery([queryKey, queryWithTypeParam])

  const navigate = useNavigate()

  const typeToNameMapping = {
    categories: 'Top-Level Categories',
    assets: 'Asset Categories',
    'warning-slates': 'Warning Slate Groups',
  }

  return (
    <>
      <Tabs
        className='mb-4'
        list={['categories', 'assets', 'warning-slates'].map((type) => {
          return {
            name: typeToNameMapping[type],
            selected: selectedTab === type,
            onClick: () => setSelectedTab(type),
          }
        })}
      />
      <Table
        status={{
          pending: isLoading || !airline,
          error: error || airlineError,
          errorMessage: error?.message || airlineError?.message,
        }}
        widths={[100, 600, 100, 65]}
        header={{
          columns: [
            { text: 'Title', key: 'title' },
            { text: 'Items', key: 'category_items' },
            { text: 'Count', key: 'count' },
            { text: '' },
          ],
          searching,
          sorting,
          filtering,
        }}
        body={{
          data: categories,
          row: {
            compact: true,
            spaced: true,
            langID: langID,
            onClick: (event, data) => navigate(`${data.id}/items`),
            render: [
              (data, index, { langID }) => data.category_values?.find((v) => v.language_id === langID)?.value || '',
              (data, index, { langID }) => (
                <div className='mr-12'>
                  {[
                    ...data.category_items,
                    ...data.child_categories.map((item) => item.category_values.find((v) => v.language_id === langID)),
                  ]
                    .map((item) => item?.title || item?.value)
                    .join(' - ')}
                </div>
              ),
              (data) => data.category_items.length,
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [
                      {
                        text: 'Delete',
                        className: 'text-red-500',
                        onClick: () =>
                          deleteModel.mutate({
                            endpoint: 'categories',
                            id: data.id,
                            onSuccess: () => {
                              queryClient.invalidateQueries([queryKey])
                            },
                          }),
                      },
                    ],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: `No ${typeToNameMapping[selectedTab]} found!`,
            text: `Create a new ${typeToNameMapping[selectedTab]} to begin.`,
            icon: 'category',
          },
        }}
      />
    </>
  )
}

export default Categories
