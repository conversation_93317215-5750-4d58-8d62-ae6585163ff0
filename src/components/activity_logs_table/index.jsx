import { useQuery } from '@tanstack/react-query'
import { Icon, Table } from '@bitcine/cinesend-theme'
import useTableFilters from '../../hooks/use_table_filters'
import { convertToLocalDateTime } from '../../helpers/convert_date'
import { useNavigate } from 'react-router-dom'

const sortingOptions = [
  { label: 'Created at', key: 'created_at' },
  { label: 'Description', key: 'description' },
]

export default function ActivityLogsTable({ queryKey }) {
  const { queryString, searching, sorting, filtering, pagination } = useTableFilters({
    sortingOptions,
    enableSearch: true,
  })
  const { data, error, isLoading } = useQuery([`activity-logs${queryKey ? `?${queryKey}` : ''}`, queryString])
  const navigate = useNavigate()
  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={[200, 300, 240, 180]}
      header={{
        columns: [
          { text: 'User', key: '' },
          { text: 'Description', key: 'description' },
          { text: 'Model' },
          { text: 'Created At', key: 'created_at' },
        ],
        searching,
        sorting,
        filtering,
      }}
      body={{
        data: data?.data,
        row: {
          compact: false,
          spaced: true,
          render: [
            (data) => (
              <div className='text-xs'>
                <div>{data.causer?.name ?? 'System Created'}</div>
                <div className='text-gray-600 italic text-2xs'>{data.causer?.email}</div>
              </div>
            ),
            (data) => <div className='first-letter:uppercase text-xs'>{data.activity_description}</div>,
            (data) =>
              data.subject_route && data.subject_title ? (
                <div
                  className='cursor-pointer hover:text-black group text-gray-600 text-xs'
                  onClick={() => navigate(data.subject_route)}
                >
                  <div className='flex items-center space-x-1'>
                    <div className='group-hover:underline'>{data.subject_title}</div>
                    <Icon icon='arrow_right_alt' className='text-sm' />
                  </div>
                  <div className='text-2xs italic'>{data.subject_model_name}</div>
                </div>
              ) : null,
            (data) => <div className='text-xs'>{convertToLocalDateTime(data.created_at)}</div>,
          ],
        },
        empty: {
          title: 'No activity logs found!',
          icon: 'list_alt',
        },
      }}
      paginate={{
        ...pagination,
        totalRows: data?.total,
        currentPage: data?.current_page - 1,
        rowsPerPage: data?.per_page,
      }}
    />
  )
}
