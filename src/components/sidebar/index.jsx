import { Sidebar as SidebarComponent } from '@bitcine/cinesend-theme'
import { useNavigate, useLocation, useParams } from 'react-router-dom'
import AirlineCycleDropdowns from './airline_cycle_dropdowns'
import useUtilities from '../../hooks/use_utilities'
import { useQuery } from '@tanstack/react-query'
import ViewMetadataButton from './view_metadata_button'
import { useSidebarDropdownState } from '../../hooks/use_sidebar_dropdown_state'
import Logo from './viasat_white_logo.png'

function Sidebar({ user, logOut }) {
  const navigate = useNavigate()
  const { assetTypes } = useUtilities()
  const { airlineID, cycleID } = useParams()

  const fullPathname = useLocation().pathname

  const root = `/airlines/${airlineID}/cycles/${cycleID}`
  let pathname = fullPathname.replace(root, '')

  // Use custom hook to manage metadata dropdown state
  const { shouldExpandMetadataDropdown } = useSidebarDropdownState()

  const { data: cycle } = useQuery(['cycles/', cycleID])
  const availableAssetTypes = cycle
    ? assetTypes
      ?.filter((assetType) => cycle?.airline?.asset_types?.includes(assetType.value))
      .map((assetType) => ({
        text: assetType.plural_label,
        to: `/metadata/${assetType.route_value}`,
        route: `${root}/metadata/${assetType.route_value}`,
      }))
    : []

  const viewSettings = pathname.startsWith('/settings')
  if (viewSettings) {
    pathname = pathname.replace('/settings', '')
  }

  const airlineCycleLinks = !cycleID
    ? []
    : [
      {
        to: '/dashboard',
        route: `${root}/dashboard`,
        text: 'Dashboard',
        icon: 'dashboard',
      },
      {
        to: '/issues',
        route: `${root}/issues`,
        text: 'Issues',
        icon: 'warning',
      },
      {
        to: '/details',
        route: `${root}/details`,
        text: 'Details',
        icon: 'date_range',
      },
      {
        to: '/images',
        route: `${root}/images`,
        text: 'Bulk Upload Images',
        icon: 'image',
      },
      {
        text: 'Content Sets',
        to: `/categories`,
        route: `${root}/categories`,
        icon: 'category',
      },
      {
        text: 'Lists',
        to: `/lists`,
        route: `${root}/lists`,
        icon: 'list_alt',
      },
      {
        to: '/metadata',
        text: 'Metadata',
        route: `${root}/metadata`,
        icon: 'list',
        children: availableAssetTypes,
        expanded: shouldExpandMetadataDropdown,
      },
      {
        text: 'Ads',
        to: `/ads`,
        route: `${root}/ads`,
        icon: 'video_library',
      },
    ]

  const settingsLinks = !user.has_unrestricted_edit_role
    ? []
    : [
      {
        to: '/airlines',
        route: '/settings/airlines',
        text: 'Airlines',
        icon: 'flight',
      },
      {
        to: '/cycles',
        route: '/settings/cycles',
        text: 'Cycles',
        icon: 'date_range',
      },
      {
        to: '/schemas',
        route: '/settings/schemas',
        text: 'Schemas',
        icon: 'list',
      },
      {
        to: '/validators',
        route: '/settings/validators',
        text: 'Validators',
        icon: 'verified_user',
      },
      {
        to: '/users',
        route: '/settings/users',
        text: 'Users',
        icon: 'person',
      },
      {
        to: '/organizations',
        route: '/settings/organizations',
        text: 'Organizations',
        icon: 'apartment',
      },
      {
        to: '/activity-logs',
        route: '/settings/activity-logs',
        text: 'Activity Logs',
        icon: 'browse_activity',
      },
    ]

  return (
    <div className='flex dark h-full'>
      <SidebarComponent
        header={{
          logo: <img src={Logo} className='w-full' />,
        }}
        secondaryHeader={viewSettings ? <ViewMetadataButton /> : <AirlineCycleDropdowns />}
        pathname={pathname}
        small
        lockExpanded={true}
        links={(viewSettings ? settingsLinks : airlineCycleLinks)
          .filter((opt) => !opt.hide)
          .map((opt) => ({
            ...opt,
            onClick: () => navigate(opt.route || opt.to),
            children: opt.children?.map((child) => ({
              ...child,
              onClick: () => navigate(child.route),
            })),
          }))}
        footer={{
          name: user?.name,
          organization: user?.organization?.name,
          dropdownContent: [
            {
              text: 'My Account',
              icon: 'person',
              onClick: () => navigate('/settings/account'),
              show: true,
            },
            {
              text: 'Settings',
              icon: 'settings',
              onClick: () => {
                localStorage.setItem('backToMetadata', fullPathname)
                navigate('/settings')
              },
              show: user.has_unrestricted_edit_role,
            },
            {
              text: 'Log Out',
              icon: 'logout',
              onClick: () => logOut.mutate(),
              show: true,
            },
          ].filter((opt) => opt.show),
        }}
      />
    </div>
  )
}

export default Sidebar
