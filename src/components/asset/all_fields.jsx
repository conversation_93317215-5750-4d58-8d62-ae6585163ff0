import CommonFields from './common_fields'
import LocalizableFields from './localizable_fields'
import StandardFields from './standard_fields'
import AssignedCategories from './categories/assigned_categories'

const AllFields = ({ asset, airlineID, cycleID }) => {
  return (
    <>
      <StandardFields asset={asset} cycleID={cycleID} />
      <LocalizableFields assetID={asset.id} airlineID={airlineID} cycleID={cycleID} />
      <p className='font-semibold h-6 my-4'>Non-localized</p>
      <CommonFields assetID={asset.id} cycleID={cycleID} />
      <AssignedCategories
        availableCategories={asset.available_categories}
        categories={asset.categories}
        assetID={asset.id}
        assetType={asset.asset_type}
        cycleID={cycleID}
        issue={asset.asset_issues.find((issue) => issue.validator_group === 'category')}
      />
    </>
  )
}

export default AllFields
