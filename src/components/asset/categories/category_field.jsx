import { useEffect, useState } from 'react'
import { Select } from '@bitcine/cinesend-theme/dist'
import useStore from '../../../hooks/use_store'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import axios from 'axios'

const CategoryField = ({ title, options, value, assetID, cycleID, langID, type }) => {
  const [currentValues, setCurrentValues] = useState([])
  const [category, setCategory] = useState()
  const { createModel } = useStore()
  const queryClient = useQueryClient()
  const [errorMessage, setErrorMessage] = useState(null)

  useEffect(() => {
    setCurrentValues(value)
  }, [value])

  const updateAssetCategory = (newValues) => {
    // if its not an array then we're dealing with a single item Select
    if (!Array.isArray(newValues)) {
      addCategory(newValues)
      return
    }

    if (currentValues.length < newValues.length) {
      // get difference and add
      const category = newValues.filter((item) => !currentValues.includes(item))[0]
      addCategory(category)
    } else {
      // get difference and remove
      const category = currentValues.filter((item) => !newValues.includes(item))[0]
      setCategory(category)
      removeCategory.mutate()
    }
  }

  const addCategory = (category) => {
    createModel.mutate(
      {
        endpoint: `categories/${category.value}/add-items`,
        data: {
          type: type === 'warning-slates' ? 'warning-slates' : 'assets', // just pass `type` or is this a necessary validation to avoid using `categories` type categories?
          ids: [assetID],
          cycleID: cycleID,
        },
      },
      {
        onSuccess: () => {
          setErrorMessage(null)
          setCurrentValues([...currentValues, category])
          queryClient.invalidateQueries([`cycles/${cycleID}/assets/${assetID}`])
        },
        onError: (err) => {
          console.error(err)
          setErrorMessage(err?.response?.data?.error ?? 'unknown error')
        },
      }
    )
  }

  const removeCategory = useMutation(
    () =>
      axios.delete(`/categories/${category.value}/remove-items`, {
        data: { ids: [assetID], cycleID: cycleID, type: type === 'warning-slates' ? 'warning-slates' : 'assets' },
      }),
    {
      onSuccess: () => {
        setCurrentValues(currentValues.filter((item) => item !== category))
        queryClient.invalidateQueries([`cycles/${cycleID}/assets/${assetID}`])
      },
      onError: (err) => console.error(err),
    }
  )

  const getTitle = (category) => {
    return category.category_values?.find((v) => v.language_id === langID)?.value
  }

  return (
    <Select
      label={title}
      options={options.map((item) => ({ value: item.id, label: getTitle(item) }))}
      value={currentValues}
      isMulti={type !== 'warning-slates'}
      onChange={(value) => updateAssetCategory(value)}
      className={'pt-2'}
      error={!!errorMessage}
      message={errorMessage}
    />
  )
}

export default CategoryField
