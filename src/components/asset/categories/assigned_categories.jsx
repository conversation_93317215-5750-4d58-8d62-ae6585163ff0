import React from 'react'
import CategoryField from './category_field'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Message } from '@bitcine/cinesend-theme/dist'

const AssignedCategories = ({ availableCategories, categories, assetID, cycleID, issue, assetType }) => {
  const params = useParams()
  const lang = params['*']
  const { data: languages } = useQuery(['/api/languages'])
  const langID = languages?.find((v) => v.ife_code === lang)?.id

  const getTitle = (category) => {
    return category.category_values?.find((v) => v.language_id === langID)?.value || ''
  }

  const getCategoryValues = (category) => {
    const categoriesValue = []

    category.child_categories.map((item) => {
      const cat = mapCategoryToValue(item)
      if (cat) {
        categoriesValue.push(cat)
      }
    })
    return categoriesValue
  }

  const mapCategoryToValue = (category) => {
    const cat = categories.find((cat) => cat.id === category.id)
    if (!cat) {
      return null
    }

    return { label: getTitle(cat), value: cat.id }
  }

  return (
    <div>
      <p className='py-2'>Categories</p>
      {issue && (
        <Message error={true} small={true}>
          {issue?.description}
        </Message>
      )}
      {assetType !== 'ad' &&
        availableCategories.map((category) => (
          <CategoryField
            title={getTitle(category)}
            options={category.child_categories}
            value={getCategoryValues(category)}
            key={category.id}
            assetID={assetID}
            cycleID={cycleID}
            langID={langID}
            type='assets'
          />
        ))}
      {assetType === 'ad' && (
        <CategoryField
          title='Warning Slate'
          options={availableCategories}
          value={categories.map(mapCategoryToValue)}
          key='warning-slate'
          assetID={assetID}
          cycleID={cycleID}
          langID={langID}
          type='warning-slates'
        />
      )}
    </div>
  )
}

export default AssignedCategories
