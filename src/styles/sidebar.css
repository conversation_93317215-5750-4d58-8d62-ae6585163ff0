/* Custom styles for sidebar dropdown expansion */

/* Force expansion of metadata dropdown when needed */
.force-expanded {
  /* Ensure dropdown content is visible */
}

.force-expanded [aria-expanded="false"] {
  /* Override collapsed state */
  aria-expanded: true !important;
}

.force-expanded .dropdown-content,
.force-expanded .nav-children,
.force-expanded [role="menu"] {
  /* Force visibility of dropdown content */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  max-height: none !important;
  overflow: visible !important;
}

/* Ensure smooth transitions for dropdown expansion */
.nav-item .dropdown-content,
.nav-item .nav-children {
  transition: all 0.2s ease-in-out;
}

/* Style for expanded state indicator */
.metadata-dropdown-expanded {
  /* Add any specific styling for when metadata dropdown is expanded */
}
