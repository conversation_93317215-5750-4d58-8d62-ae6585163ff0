import { Route, Routes, useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Status } from '@bitcine/cinesend-theme/dist'
import Asset from '../../components/asset'
import AssetsByType from '../../components/assets_table'

const Ads = () => {
  const params = useParams()
  const { data: cycle, isLoading, error } = useQuery(['cycles/', params.cycleID])
  return (
    <Status pending={isLoading} error={error}>
      <Routes>
        <Route
          path='/'
          element={<AssetsByType cycle={cycle} assetType={{ value: 'ad', label: 'Ad', plural_label: 'Ads' }} />}
        />
        <Route path='/:assetID/*' element={<Asset cycle={cycle} />} />
      </Routes>
    </Status>
  )
}

export default Ads
