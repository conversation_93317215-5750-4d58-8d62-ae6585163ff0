import { Button, Icon } from '@bitcine/cinesend-theme'
import { useParams } from 'react-router-dom'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import Details from './details'
import useStore from '../../hooks/use_store'
import Page from '../../components/layouts/page'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const Cycle = () => {
  const { updateModel } = useStore()
  const params = useParams()
  const queryClient = useQueryClient()
  const { data: cycle, isLoading, error } = useQuery(['cycles/', params.cycleID])
  const { canEditModel: canEditCycle } = useAuth(modelTypes.Cycle, cycle)

  return (
    <Page
      title={cycle?.description}
      pending={isLoading}
      error={error}
      buttons={[
        canEditCycle && (
          <Button
            key={cycle?.id + 'autorenew'}
            tertiary
            size='small'
            icon={!updateModel.isLoading && 'autorenew'}
            onClick={() =>
              updateModel.mutate(
                {
                  endpoint: 'cycles',
                  id: params.cycleID + '/auto-fill',
                },
                {
                  onSuccess: () => {
                    queryClient.invalidateQueries(['cycles/', params.cycleID])
                  },
                }
              )
            }
          >
            {updateModel.isLoading && (
              <Icon key={cycle?.id + 'Icon'} icon='autorenew' className='animate-spin-slow text-base mr-2' />
            )}
            Auto-Fill
          </Button>
        ),
      ]}
    >
      {cycle ? <Details cycle={cycle} /> : null}
    </Page>
  )
}

export default Cycle
