import React, { useState } from 'react'
import { Button, Input, Modal } from '@bitcine/cinesend-theme/dist'
import useStore from '../../../hooks/use_store'

export default function NewIssue({ type, cycleID }) {
  const [openIssueModal, setOpenIssueModal] = useState(false)
  const [issue, setIssue] = useState('')
  const { createModel } = useStore()

  const createIssue = () => {
    createModel.mutate({
      endpoint: 'issues',
      data: {
        description: issue,
        type: type,
        is_custom: 1,
        cycle_id: cycleID,
      },
      onSuccess: () => setOpenIssueModal(false),
      onError: (error) => console.error(error.message),
    })
  }

  return (
    <div>
      <Button size='medium' type='primary' onClick={() => setOpenIssueModal(true)}>
        New issue
      </Button>
      {openIssueModal && (
        <Modal
          header='New Issue'
          confirmButton={{
            text: 'Submit',
            onClick: () => createIssue(),
          }}
          onClose={() => setOpenIssueModal(null)}
        >
          <Input
            label='Describe the issue'
            placeholder='the issue...'
            type='text'
            value={issue}
            onChange={(e) => setIssue(e.target.value)}
            onKeyPress={(e) => e.charCode === 13 && !!e.target.value && createIssue()}
            autoFocus
          />
        </Modal>
      )}
    </div>
  )
}
