import { useNavigate, useParams } from 'react-router-dom'
import { ButtonDropdown, SegmentedControl, Table, Status, Button, Tag } from '@bitcine/cinesend-theme/dist'
import Page from '../../components/layouts/page'
import { useState } from 'react'
import NewIssue from './components/new_issue'
import { useQueryClient, useQuery } from '@tanstack/react-query'
import useStore from '../../hooks/use_store'

export default function Issues() {
  const navigate = useNavigate()
  const [selectedValue, setSelectedValue] = useState('metadata')
  const [perPage, setPerPage] = useState(10)
  const [page, setPage] = useState(0)
  const [search, setSearch] = useState('')
  const [sorting, setSorting] = useState({ direction: 'desc', key: 'created_at' })
  const airlineID = useParams().airlineID
  const cycleID = useParams().cycleID
  const {
    data: issues,
    isLoading,
    errors,
  } = useQuery([
    `issues`,
    `?cycleID=${cycleID}&type=${selectedValue}&per_page=${perPage}&page=${page}&sort_key=${sorting.key}&sort_direction=${sorting.direction}&search=${search}`,
  ])
  const { deleteModel } = useStore()
  const queryClient = useQueryClient()

  const setValue = (v) => {
    setSelectedValue(v)
    setPage(0)
  }

  const getCounterForLabel = (label) => {
    const count = issues?.issues_count.find((issue) => issue.type === label)?.count
    return count ? `${label} (${count})` : label
  }

  const goToAsset = (asset, fieldValueID = null) => {
    const root = `/airlines/${airlineID}/cycles/${cycleID}/metadata/${asset.route_value}/${asset.id}`
    if (fieldValueID) {
      // TODO: go to the asset and scroll to the field you want to fix - CS-1373
      navigate(`${root}#field-value-${fieldValueID}`)
    } else {
      navigate(root)
    }
  }

  return (
    <Page title='Issues' buttons={[<NewIssue key={'new-issue'} type={selectedValue} cycleID={cycleID} />]}>
      <SegmentedControl
        options={[
          { label: getCounterForLabel('metadata'), value: 'metadata' },
          { label: getCounterForLabel('images'), value: 'images' },
          { label: getCounterForLabel('content'), value: 'content' },
        ]}
        value={selectedValue}
        onChange={setValue}
        type={'primary'}
        className={'mb-2 [&>button]:capitalize'}
      />
      <Table
        status={{
          pending: isLoading,
          error: errors,
          errorMessage: errors?.message,
        }}
        draggable={false}
        widths={[100, 200, 200, 200, 120, 75]}
        header={{
          columns: [
            {
              text: 'Severity',
              key: 'severity',
            },
            {
              text: 'Description',
              key: 'description',
            },
            {
              text: 'Asset',
              key: 'asset',
            },
            {
              text: 'Created At',
              key: 'created_at',
            },
            {
              text: 'Created By',
              key: 'created_by',
            },
            {
              text: 'Actions',
            },
          ],
          sorting: {
            onSortChange: (newSorting) => setSorting(newSorting),
            direction: sorting.direction,
            key: sorting.key,
          },
          searching: {
            searchPlaceholder: 'Enter a search term here...',
            search,
            onSearch: (term) => setSearch(term),
          },
        }}
        body={{
          data: issues?.data,
          row: {
            compact: true,
            spaced: true,
            truncate: true,
            render: [
              (data) => (
                <Tag
                  label={data.severity.toUpperCase()}
                  type={data.severity == 'error' ? 'error' : 'warning'}
                  outline={true}
                />
              ),
              (data) => data.description,
              (data) => (
                <div className='cursor-pointer hover:underline' onClick={() => goToAsset(data.asset)}>
                  {data.asset?.title}
                </div>
              ),
              (data) => data.created_at,
              (data) => data.created_by ?? 'System',
              (data) => (
                <Button
                  onClick={() => goToAsset(data.asset, data.field_value_id)}
                  size='small'
                  type='neutral'
                  tertiary={true}
                  disabled={!data.asset}
                >
                  Edit
                </Button>
              ),
            ],
          },
          empty: {
            title: 'No issues found!',
            text: 'We could not find any issues at the moment',
            icon: 'check',
          },
        }}
        paginate={{
          currentPage: page,
          totalRows: issues?.meta.total,
          rowsPerPage: perPage,
          page: issues?.meta.current_page,
          onPageChange: (page) => setPage(page),
          onRowsPerPageChange: (perPage) => {
            setPerPage(perPage)
            setPage(0)
          },
        }}
      />
    </Page>
  )
}
