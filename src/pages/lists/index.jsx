import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import Page from '../../components/layouts/page'
import Lists from '../schema/lists'
import { Status } from '@bitcine/cinesend-theme/dist'

const ListPage = () => {
  const { airlineID, cycleID } = useParams()
  const { data: cycle, isLoading: isCycleLoading } = useQuery(['cycles/', cycleID])
  const schemaId = cycle?.schema_id
  const { data: schema, isLoading: isSchemaLoading } = useQuery({
    queryKey: [`schemas/`, schemaId],
    enabled: !!schemaId,
  })
  const root = `/airlines/${airlineID}/cycles/${cycleID}`

  return (
    <Page title='Lists'>
      <Status pending={isCycleLoading || isSchemaLoading} error={!cycle || !schema}>
        <Lists schema={schema} />
      </Status>
    </Page>
  )
}

export default ListPage
