import { Select, Input, FileUpload, Checkbox } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import useStore from '../../hooks/use_store'
import { useSnackbar } from '../../hooks/use_snackbar'
import useUtilities from '../../hooks/use_utilities'
import AirlineSnapshotSizeInput from '../../components/airline_snapshot_size_input'

const Details = ({ airline }) => {
  const { addErrorMessage } = useSnackbar()
  const { data: languages } = useQuery(['/api/languages'])
  const { data: cycleFrequencies } = useQuery(['/api/cycle-frequencies'])
  const { assetTypes } = useUtilities()
  const { updateModel } = useStore()
  const [state, setState] = useState(airline)
  const updateState = (data) => setState({ ...state, ...data })
  const queryClient = useQueryClient()
  const saveModel = (data) => {
    let newState = {
      ...data,
    }

    if (newState.languages) {
      // ensure state object remains consistent with response structure
      newState.languages = languages.filter((lang) => data.languages.includes(lang.id))

      // if default language was removed from the list, also clear out the db field
      if (!data.languages.includes(state.default_language_id)) {
        newState.default_language_id = null
        data.default_language_id = null
      }
    }

    updateState(newState)
    updateModel.mutate(
      {
        endpoint: 'airlines',
        id: airline.id,
        data,
      },
      {
        onSuccess: () => queryClient.setQueryData(['airlines/', airline.id], { ...airline, ...data }),
      }
    )
  }
  const pluralAssetTypes = assetTypes?.map((opt) => ({ ...opt, label: opt.plural_label }))
  const mapLanguageToOption = (language) => ({ value: language.id, label: language.eng_description })
  return (
    <div className='grid grid-cols-2 gap-3'>
      <div className='space-y-4 mt-4'>
        <Input
          label='Name'
          value={state?.name}
          onChange={(e) => updateState({ name: e.target.value })}
          onBlur={() => saveModel({ name: state.name })}
        />

        <Input
          label='Group code'
          value={state?.group_code}
          onChange={(e) => updateState({ group_code: e.target.value })}
          onBlur={() => saveModel({ group_code: state.group_code })}
        />

        <Select
          label='Asset Types'
          placeholder='Movies, series, audiobooks, etc.'
          options={pluralAssetTypes}
          isMulti
          value={pluralAssetTypes?.filter((opt) => state?.asset_types?.includes(opt.value))}
          onChange={(options) => {
            saveModel({ asset_types: options.map((v) => v.value) })
          }}
        />

        <Select
          label='Cycle Frequency'
          placeholder='(monthly, every 2-months, quarterly, etc.)'
          className={'w-full'}
          options={cycleFrequencies}
          value={cycleFrequencies?.find((opt) => opt.value === state.cycle_frequency)}
          onChange={(value) => {
            saveModel({ cycle_frequency: value.value })
          }}
        />

        <Select
          label='Portal Languages'
          placeholder={'Portal languages'}
          className={'w-full'}
          options={languages ? languages.map(mapLanguageToOption) : []}
          value={languages
            ?.filter((obj1) => state.languages?.some((obj2) => obj1.id === obj2.id))
            .map(mapLanguageToOption)}
          isMulti={true}
          onChange={(list) => {
            const langs = list.map((v) => v.value)
            saveModel({ languages: langs })
          }}
        />
        <Select
          label='Default Language'
          className={'w-full'}
          options={state.languages?.map(mapLanguageToOption) ?? []}
          value={state.languages
            ?.map(mapLanguageToOption)
            ?.find((option) => option.value === airline.default_language_id)}
          isMulti={false}
          onChange={(lang) => {
            saveModel({ default_language_id: lang.value })
          }}
        />
        <Input
          label='Recall Period (Days)'
          value={state.recall_period}
          type='number'
          onChange={(e) => updateState({ recall_period: e.target.value })}
          onBlur={() => saveModel({ recall_period: state.recall_period })}
        />
      </div>
      <div className='space-y-4 mt-4'>
        <FileUpload
          className={'h-40'}
          backgroundImage={{
            url: state.logo_url,
          }}
          includeRemoveButton={false}
          upload={{
            message: 'Drop image here',
            accept: {
              'image/*': ['.jpg', '.jpeg', '.png'],
            },
            icon: 'image',
            publicURL: `${import.meta.env.VITE_API_URL}/api/airlines/upload-logo/${airline.id}`,
            onComplete: () => {
              // this is a bit of a hack, but we need to refetch the airline data after uploading the logo
              queryClient.fetchQuery(['airlines/', airline.id]).then((res) => updateState(res))
            },
            onError: (err) => {
              addErrorMessage(err.message)
            },
          }}
          button={{
            text: 'Upload Logo',
          }}
        />

        <AirlineSnapshotSizeInput
          fieldName='max_total_snapshot_size'
          label='Max Snapshot Size - Total'
          value={state?.max_total_snapshot_size}
          saveModel={saveModel}
        />

        <AirlineSnapshotSizeInput
          fieldName='max_delta_snapshot_size'
          label='Max Snapshot Size - Delta'
          value={state?.max_delta_snapshot_size}
          saveModel={saveModel}
        />

        <AirlineSnapshotSizeInput
          fieldName='max_for_eis_size'
          label='Max "For EIS" Size'
          value={state?.max_for_eis_size}
          saveModel={saveModel}
        />
        <fieldset className='p-2 border rounded-lg flex items-center space-x-2'>
          <legend className='text-xs'>Ad / Warning Slate Settings</legend>
          <Checkbox
            className='w-full'
            label='Is Fast-Forwardable?'
            checked={state?.is_ad_fast_forwardable}
            onChange={(e) => {
              saveModel({ is_ad_fast_forwardable: e.target.checked })
            }}
          />
          <Input
            label='Skippable After (seconds)'
            value={state?.ad_skippable_after_seconds}
            onChange={(e) => updateState({ ad_skippable_after_seconds: e.target.value })}
            onBlur={() => saveModel({ ad_skippable_after_seconds: state.ad_skippable_after_seconds })}
          />
        </fieldset>
      </div>
    </div>
  )
}

export default Details
