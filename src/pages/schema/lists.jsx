import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button, ButtonDropdown, Icon, Table } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useNavigate } from 'react-router-dom'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const Lists = ({ schema }) => {
  const { createModel, deleteModel } = useStore()
  const [search, setSearch] = useState('')
  const { data: lists, error, isLoading } = useQuery(['collections', `?schemaID=${schema.id}`, `&search=${search}`])
  const { canEditModel: canEditCollection } = useAuth(modelTypes.Collection)
  const navigate = useNavigate()
  const createList = () => {
    createModel.mutate(
      { endpoint: 'collections', data: { name: 'New List', schema_id: schema.id } },
      {
        onSuccess: (res) => {
          navigate(`${res.data.collection.id}`)
        },
      }
    )
  }
  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={['auto', 100, 60]}
      header={{
        columns: [{ text: 'Name', key: 'name' }, { text: 'Viasat Only', key: 'viasat_only' }, { text: '' }],
        searching: {
          searchPlaceholder: 'Enter a search term here...',
          search,
          onSearch: (term) => setSearch(term),
        },
        customElement: canEditCollection && (
          <Button icon='add_circle_outline' onClick={() => createList()}>
            Create new list
          </Button>
        ),
      }}
      body={{
        data: lists,
        row: {
          compact: true,
          spaced: true,
          onClick: (event, data) => navigate(`${data.id}`),
          render: [
            (data) => data.name,
            (data) => (data.viasat_only ? <Icon icon='check' /> : ''),
            (data) => (
              <ButtonDropdown
                kebab
                dropdown={{
                  content: [
                    {
                      text: 'Delete',
                      className: 'text-red-500',
                      onClick: () => deleteModel.mutate({ endpoint: 'collections', id: data.id }),
                    },
                  ],
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No lists found!',
          text: 'Create a new airline to begin.',
          icon: 'flight',
        },
      }}
    />
  )
}

export default Lists
