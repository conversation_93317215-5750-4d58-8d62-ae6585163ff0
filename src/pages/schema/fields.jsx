import { ButtonDropdown, Table, Tag, Icon } from '@bitcine/cinesend-theme/dist'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useNavigate, useParams } from 'react-router-dom'
import { useState } from 'react'
import useStore from '../../hooks/use_store'
import useUtilities from '../../hooks/use_utilities'

// Maybe pull from backend, or not since it's a very limited list?
const FIELD_TYPES = ['String', 'Boolean', 'Datetime', 'Dropdown', 'Image', 'Video', 'Audio', 'Number']

const Fields = () => {
  const navigate = useNavigate()
  const schemaID = useParams().schemaID
  const [search, setSearch] = useState('')
  const queryClient = useQueryClient()
  const { assetTypes } = useUtilities()
  const { data: list, isLoading, isError, error } = useQuery([`fields?schemaID=${schemaID}`, `&search=${search}`])
  const { createModel, deleteModel } = useStore()
  const createField = (type) => {
    // Default to all available asset types since root schema is used by many airlines
    const defaultAssetTypes = assetTypes?.map((assetType) => assetType.value) || []

    createModel.mutate(
      {
        endpoint: 'fields',
        data: {
          schema_id: schemaID,
          name: 'New Field',
          field_type: type.toLowerCase(),
          asset_types: defaultAssetTypes,
        },
      },
      {
        onSuccess: () => queryClient.invalidateQueries([`fields?schemaID=${schemaID}`]),
      }
    )
  }
  const deleteField = (id) => {
    deleteModel.mutate(
      {
        endpoint: 'fields',
        id,
      },
      {
        onSuccess: () => queryClient.invalidateQueries([`fields?schemaID=${schemaID}`]),
      }
    )
  }

  const prepareValidators = (validators) => {
    return (
      <div className='flex flex-wrap gap-1'>
        {validators.map((validator) => {
          const label =
            validator.parameters !== null ? `${validator.name}: ${validator.parameters.value}` : validator.name
          return <Tag outline type='neutral' size='medium' label={label} key={validator.id} />
        })}
      </div>
    )
  }

  return (
    <>
      <div className='border bg-info-100 p-4 text-xs rounded my-4'>
        This is the list of fields that are attached to this schema. A schema can be added to any cycle, but once any
        metadata is populated, that schema is locked and fields cannot be modified.
      </div>
      <Table
        status={{
          pending: isLoading,
          error: isError,
          errorMessage: error?.message,
        }}
        widths={[200, 200, 200, 200, 'auto', 65]}
        header={{
          columns: [
            { text: 'Field identifier', key: 'name' },
            { text: 'Field type', key: 'field_type' },
            { text: 'Asset types', key: 'asset_types' },
            { text: 'Field Flags', key: '' },
            { text: 'Validators', key: '' },
            { text: '' },
          ],
          searching: {
            searchPlaceholder: 'Enter a search term here...',
            search,
            onSearch: (term) => setSearch(term),
          },
          customElement: (
            <ButtonDropdown
              button={{
                text: 'Add Field',
                icon: 'add',
              }}
              dropdown={{
                content: FIELD_TYPES.map((type) => ({
                  text: type,
                  onClick: () => createField(type),
                })),
              }}
            />
          ),
        }}
        body={{
          data: list?.data,
          row: {
            compact: true,
            spaced: true,
            onClick: (_, data) => navigate(`${data.id}`),
            render: [
              (data) => data.name,
              (data) => <Tag outline label={data.field_type.toUpperCase()} />,
              (data) => (
                <Tag
                  outline
                  type='warning'
                  label={getAssetTypes(assetTypes || [], data.asset_types || []).toUpperCase()}
                />
              ),
              (data) => (
                <div className='flex flex-wrap gap-1 p-1'>
                  {data.is_size_field == true && (
                    <Tag
                      outline
                      type='info'
                      size='small'
                      label={
                        <div className='flex'>
                          <Icon icon='check' className='text-sm pr-1' />
                          Size field
                        </div>
                      }
                    />
                  )}
                  {data.is_localizable == true && (
                    <Tag
                      outline
                      type='info'
                      size='small'
                      label={
                        <div className='flex'>
                          <Icon icon='check' className='text-sm pr-1' />
                          Localizable
                        </div>
                      }
                    />
                  )}
                  {data.is_external_api_value_source == true && (
                    <Tag
                      outline
                      type='info'
                      size='small'
                      label={
                        <div className='flex'>
                          <Icon icon='check' className='text-sm pr-1' />
                          External Source
                        </div>
                      }
                    />
                  )}
                </div>
              ),
              (data) => prepareValidators(data.enabled_validators),
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [{ text: 'Delete', className: 'text-red-500', onClick: () => deleteField(data.id) }],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: 'No cycles found!',
            text: 'Create a new cycle to begin.',
            icon: 'date_range',
          },
        }}
      />
    </>
  )
}

const getAssetTypes = (allAssetTypes, specifiedAssetTypes) => {
  if (specifiedAssetTypes.length === 0) {
    return 'All Asset Types'
  }
  return allAssetTypes
    .filter((assetType) => specifiedAssetTypes.includes(assetType.value))
    .map((assetType) => assetType.plural_label)
    .join(', ')
}

export default Fields
