import { useNavigate } from 'react-router-dom'
import { <PERSON>, But<PERSON>, Message } from '@bitcine/cinesend-theme/dist'
import AddModal from './add_modal'
import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import axios from 'axios'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const ItemsTable = ({ category, invalidateKey, langID }) => {
  const queryClient = useQueryClient()
  const [showModal, setShowModal] = useState(false)
  const [search, setSearch] = useState('')
  const [selectedIDs, setSelectedIDs] = useState([])
  const { canEditModel: canEditCategory } = useAuth(modelTypes.Category, category)

  const items = [...category?.child_categories, ...category?.category_items].filter(
    (item) =>
      item?.title?.toLowerCase().includes(search.toLowerCase()) ||
      item?.category_values
        .find((v) => v.language_id === langID)
        .value.toLowerCase()
        .includes(search.toLowerCase())
  )

  const updateOrder = useMutation(
    (ids) => {
      return axios.put(`/categories/${category.id}/update-item-order`, { ids: ids, type: category.type })
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(invalidateKey)
      },
    }
  )

  const removeItems = useMutation(
    () => axios.delete(`/categories/${category.id}/remove-items`, { data: { ids: selectedIDs, type: category.type } }),
    {
      onSuccess: () => {
        setSelectedIDs([])
        queryClient.invalidateQueries(invalidateKey)
      },
    }
  )

  const navigate = useNavigate()

  return (
    <>
      {category.issues.length > 0 && (
        <Message error={true} small={true} className='mb-2'>
          {category.issues[0]?.description}
        </Message>
      )}
      <Table
        draggable={search === ''}
        widths={['auto', '120', '120']}
        header={{
          columns: [
            { text: 'Name', key: 'name' },
            { text: 'Start Date', key: 'start_date' },
            { text: 'End Date', key: 'end_date' },
          ],
          checkbox: canEditCategory && {
            checked: items.length > 0 && selectedIDs.length === items.length,
            indeterminate: selectedIDs.length > 0 && selectedIDs.length < items.length,
            onChange: () => {
              if (selectedIDs.length === items.length) {
                setSelectedIDs([])
              } else {
                setSelectedIDs(items.map(({ id }) => id))
              }
            },
          },
          searching: {
            searchPlaceholder: 'Search items...',
            search,
            onSearch: (term) => setSearch(term),
          },
          sorting: {},
          customElement: (
            <>
              {(!category.type || category.type === 'assets') && (
                <Button icon='add_circle_outline' onClick={() => setShowModal('assets')}>
                  Add Assets
                </Button>
              )}
              {(!category.type || category.type === 'categories') && canEditCategory && (
                <Button icon='add_circle_outline' onClick={() => setShowModal('categories')}>
                  Add Categories
                </Button>
              )}
              {(!category.type || category.type === 'warning-slates') && canEditCategory && (
                <Button icon='add_circle_outline' onClick={() => setShowModal('warning-slates')}>
                  Add Warning Slates
                </Button>
              )}
              {selectedIDs.length > 0 && canEditCategory && (
                <Button icon='remove_circle_outline' onClick={() => removeItems.mutate()} type='error'>
                  Remove ({selectedIDs.length}) Items
                </Button>
              )}
            </>
          ),
        }}
        body={{
          data: items,
          onDragEnd: (order) => {
            const ids = order.map((item) => item.id)
            updateOrder.mutate(ids)
          },
          row: {
            onClick:
              category.type === 'categories'
                ? (_, data) =>
                    navigate(`/airlines/${category.airline_id}/cycles/${category.cycle_id}/categories/${data.id}/items`)
                : null,
            checkbox: canEditCategory && {
              checked: (data) => selectedIDs.includes(data.id),
              onChange: (data) =>
                selectedIDs.includes(data.id)
                  ? setSelectedIDs(selectedIDs.filter((i) => i !== data.id))
                  : setSelectedIDs([...selectedIDs, data.id]),
            },
            render: [
              (data) => data?.category_values?.find((v) => v.language_id === langID)?.value || data.title,
              (data) => new Date(data.start_date).toLocaleDateString(),
              (data) => new Date(data.end_date).toLocaleDateString(),
            ],
          },
          empty: {
            title: 'No items found!',
            text: 'Add items to begin.',
            icon: 'date_range',
          },
        }}
      />
      {showModal && (
        <AddModal
          category={category}
          invalidateKey={invalidateKey}
          onClose={() => setShowModal(false)}
          queryKey={[
            `categories/${category.id}/get-addable-items?type=${showModal === 'warning-slates' ? 'assets' : showModal}`,
          ]}
          type={showModal}
          langID={langID}
        />
      )}
    </>
  )
}

export default ItemsTable
