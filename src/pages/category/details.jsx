import { DatePicker, Input, Message, Select, Status } from '@bitcine/cinesend-theme/dist'
import useUtilities from '../../hooks/use_utilities'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import InputLocalized from './components/input_localized'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const Details = ({ category, update }) => {
  const [currentCategory, setCurrentCategory] = useState(category)
  const { assetTypes } = useUtilities()
  const pluralAssetTypes = assetTypes?.map((opt) => ({ ...opt, label: opt.plural_label }))
  const { data: airline, isLoading, isError, error } = useQuery([`airlines/${category.airline_id}`])
  const { canEditModel: canEditCategory } = useAuth(modelTypes.Category, category)

  const disabled = !canEditCategory

  return (
    <div className='space-y-2'>
      <Status pending={isLoading} isError={isError} errorMessage={error?.message}>
        {category.issues.length > 0 && (
          <Message error={true} small={true} className='mb-2'>
            {category.issues[0]?.description}
          </Message>
        )}
        <div className='flex items-center space-x-4'>
          {airline?.languages?.map((lang) => {
            return (
              <InputLocalized
                categoryID={category.id}
                langID={lang.id}
                key={`title-${lang.id}`}
                label={`${lang?.eng_description} Title`}
                defaultValue={category.category_values?.find((v) => v.language_id === lang.id)?.value}
                disabled={disabled}
              />
            )
          })}
        </div>
      </Status>
      <Input
        label={'Viasat Unique Name'}
        value={currentCategory.viasat_unique_name}
        onChange={(e) => {
          setCurrentCategory({ ...currentCategory, viasat_unique_name: e.target.value })
        }}
        onBlur={() => {
          update({ viasat_unique_name: currentCategory.viasat_unique_name })
        }}
      />

      <Select
        label={category.type !== 'warning-slates' ? 'Asset Types' : 'Asset Type'}
        placeholder='Movies, series, audiobooks, etc.'
        options={pluralAssetTypes?.filter((opt) => airline?.asset_types?.includes(opt.value))}
        isMulti={category.type !== 'warning-slates'}
        value={pluralAssetTypes?.filter((opt) => currentCategory?.asset_types?.includes(opt.value))}
        onChange={(options) => {
          const newOptions = Array.isArray(options) ? options : [options]
          update({ asset_types: newOptions.map((v) => v.value) })
          setCurrentCategory({ ...currentCategory, asset_types: newOptions.map((v) => v.value) })
        }}
        disabled={disabled}
      />
      {!category.is_root && (
        <>
          <DatePicker
            label={'Start Date'}
            className='w-full'
            date={category.start_date}
            showTimeSelect={false}
            onChange={(newDate) => update({ start_date: newDate }, false)}
            disabled={disabled}
          />
          <DatePicker
            label={'End Date'}
            className='w-full'
            date={category.end_date}
            showTimeSelect={false}
            onChange={(newDate) => update({ end_date: newDate }, false)}
            disabled={disabled}
          />
        </>
      )}
    </div>
  )
}

export default Details
