import { Modal, Table } from '@bitcine/cinesend-theme/dist'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import axios from 'axios'
import useTableFilters from '../../hooks/use_table_filters'
import { useSnackbar } from '../../hooks/use_snackbar'

const AddModal = ({ onClose, type, queryKey, category, invalidateKey, langID }) => {
  const { addMessage } = useSnackbar()
  const [selectedIDs, setSelectedIDs] = useState([])
  const { data: items, isLoading, isError, error } = useQuery(queryKey)
  const title = ['assets', 'warning-slates'].includes(type) ? 'Assets' : 'Categories'
  const queryClient = useQueryClient()

  const { searching } = useTableFilters({ enableSearch: true })

  const filteredItems = items?.filter((item) => {
    if (searching.search) {
      return item.title.toLowerCase().includes(searching.search.toLowerCase())
    }
    return true
  })

  const addItems = useMutation(
    () => {
      return axios.post(`/categories/${category.id}/add-items`, { type, ids: selectedIDs })
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(invalidateKey)
        onClose()
      },
      onError: (err) => {
        addMessage(err?.response?.data?.error ?? err.message, 'error')
      },
    }
  )

  const toggleSelectedID = (id) => {
    selectedIDs.includes(id)
      ? setSelectedIDs(selectedIDs.filter((i) => i !== id))
      : setSelectedIDs([...selectedIDs, id])
  }

  return (
    <Modal
      header={`Add ${title}`}
      onClose={onClose}
      confirmButton={{
        text: 'Add',
        disabled: selectedIDs.length === 0,
        onClick: () => addItems.mutate(),
      }}
    >
      <div className={'max-h-96 overflow-y-scroll'}>
        <Table
          status={{
            pending: isLoading,
            error: isError,
            errorMessage: error?.message,
          }}
          header={{
            columns: [
              { text: 'Name', key: 'name' },
              { text: 'Asset Type', key: 'plural' },
            ],
            searching,
          }}
          widths={['auto', '85']}
          body={{
            data: filteredItems,
            row: {
              compact: true,
              spaced: true,
              onClick: (event, data) => toggleSelectedID(data.id),
              checkbox: {
                checked: (data) => selectedIDs.includes(data.id),
                onChange: (data) => toggleSelectedID(data.id),
              },
              render: [
                (data, index, { langID: langID }) =>
                  data.title ?? data.category_values?.find((v) => v.lang_id === langID)?.value,
                (data) => data.plural_label,
              ],
            },
            empty: {
              title: `No ${title.toLowerCase()} found!`,
              text: `Create ${title.toLowerCase()} to begin.`,
              icon: 'article',
            },
          }}
        />
      </div>
    </Modal>
  )
}

export default AddModal
