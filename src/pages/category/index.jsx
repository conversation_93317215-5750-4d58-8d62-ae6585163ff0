import { useParams, Routes, Route, Navigate } from 'react-router-dom'
import Page from '../../components/layouts/page'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import useStore from '../../hooks/use_store'
import Details from './details'
import ItemsTable from './items_table'

const Category = () => {
  const { categoryID, airlineID } = useParams()
  const { data: airline } = useQuery(['airlines/', airlineID])

  const queryClient = useQueryClient()
  const { updateModel } = useStore()
  const queryKey = ['categories/', categoryID]
  const { data: category, isLoading } = useQuery(queryKey)

  const update = (value, isLocal) => {
    if (isLocal) {
      queryClient.setQueryData(queryKey, () => ({
        ...category,
        ...value,
      }))
    } else {
      updateModel.mutate({
        endpoint: 'categories',
        id: categoryID,
        data: value,
      })
    }
  }

  return (
    <Page
      pending={isLoading}
      breadcrumbs={[
        {
          text: 'Content Sets',
          to: `/airlines/${category?.airline_id}/cycles/${category?.cycle_id}/categories`,
        },
        { text: `${category?.category_values.find((v) => v.language_id === airline?.languages[0].id)?.value || ''}` },
      ]}
      tabs={[
        { name: 'Items', to: 'items' },
        { name: 'Details', to: 'details' },
        category?.type === 'warning-slates' && { name: 'Assigned Assets', to: 'assets' },
      ].filter(Boolean)}
    >
      <Routes>
        <Route
          path='/items'
          element={<ItemsTable invalidateKey={queryKey} category={category} langID={airline?.languages[0].id} />}
        />
        <Route path='/details' element={<Details invalidateKey={queryKey} category={category} update={update} />} />
        <Route path='/assets' element={'TODO: Implement "assigned asset" table'} />
        <Route path='*' element={<Navigate to={`items`} replace={true} />} />
      </Routes>
    </Page>
  )
}

export default Category
