import { Modal, DatePicker, Input } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

const CreateForm = ({ isOpen, onClose, cycleID, createModel, categoryType }) => {
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [title, setTitle] = useState('')

  const navigate = useNavigate()

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      header={categoryType === 'assets' ? `Create Assets Category` : `Create Warning Slate Group`}
      confirmButton={{
        text: 'Create',
        disabled: !startDate || !endDate || !title,
        onClick: () =>
          createModel.mutate(
            {
              endpoint: 'categories',
              data: {
                title,
                type: categoryType,
                cycle_id: cycleID,
                is_root: false,
                start_date: startDate,
                end_date: endDate,
              },
            },
            { onSuccess: (res) => navigate(`${res.data.category.id}/items`) }
          ),
      }}
    >
      <div className='space-y-2'>
        <Input label={'Title'} value={title} onChange={(e) => setTitle(e.target.value)} />
        <DatePicker
          label={'Start Date'}
          className='w-full'
          date={startDate}
          onChange={(newDate) => setStartDate(newDate)}
        />
        <DatePicker label={'End Date'} className='w-full' date={endDate} onChange={(newDate) => setEndDate(newDate)} />
      </div>
    </Modal>
  )
}

export default CreateForm
