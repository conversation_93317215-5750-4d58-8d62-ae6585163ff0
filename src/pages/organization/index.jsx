import { useQuery, useQueryClient } from '@tanstack/react-query'
import Page from '../../components/layouts/page'
import { useParams } from 'react-router-dom'
import useStore from '../../hooks/use_store'
import { Input, Select } from '@bitcine/cinesend-theme/dist'

const Organization = () => {
  const { orgID } = useParams()
  const queryKey = ['organizations/', orgID]

  const { updateModel } = useStore()
  const queryClient = useQueryClient()

  const { data: organization, isLoading } = useQuery(queryKey)
  const { data: { data: users } = { data: [] } } = useQuery(['users'])
  const { data: airlines } = useQuery(['airlines'])

  const update = (value, isLocal) => {
    if (isLocal) {
      queryClient.setQueryData(queryKey, () => ({
        ...organization,
        ...value,
      }))
    } else {
      updateModel.mutate({
        endpoint: 'organizations',
        id: orgID,
        data: value,
      })
    }
  }

  return (
    <Page breadcrumbs={[{ text: 'Organizations', to: '/settings/organizations' }, { text: organization?.name }]}>
      <div className='flex flex-col space-y-4'>
        <Input
          value={organization?.name}
          label='Name'
          onChange={(e) => update({ name: e.target.value }, true)}
          onBlur={() => update({ name: organization?.name }, false)}
        />
        <Select
          label='Owner'
          value={
            organization?.owner_id
              ? {
                  value: organization.owner_id,
                  label: users?.find((opt) => opt.id === organization.owner_id)?.name,
                }
              : null
          }
          isLoading={isLoading}
          options={users?.map((opt) => ({ value: opt.id, label: opt.name })) || []}
          onChange={(opt) => update({ owner_id: opt.value }, false)}
        />
        <Select
          label='Airlines'
          options={airlines?.map((item) => ({ value: item.id, label: item.name }))}
          isMulti
          value={organization?.airlines?.map((a) => ({ value: a.id, label: a.name })) || []}
          onChange={(opts) => update({ airlines: opts.map((opt) => opt.value) }, false)}
        />
      </div>
    </Page>
  )
}

export default Organization
