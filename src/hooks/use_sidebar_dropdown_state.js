import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

/**
 * Custom hook to manage sidebar dropdown state for metadata navigation
 * Ensures the metadata dropdown is expanded when on metadata routes
 */
export const useSidebarDropdownState = () => {
  const location = useLocation()

  useEffect(() => {
    // Check if we're on a metadata route
    const isOnMetadataRoute = location.pathname.includes('/metadata')
    
    if (isOnMetadataRoute) {
      // Set flag to expand metadata dropdown
      localStorage.setItem('metadataDropdownExpanded', 'true')
      
      // Use a small delay to ensure the sidebar has rendered
      const timer = setTimeout(() => {
        expandMetadataDropdown()
      }, 100)
      
      return () => clearTimeout(timer)
    } else if (!location.pathname.includes('/settings')) {
      // Only clear the flag if we're not in settings (since settings is temporary)
      localStorage.removeItem('metadataDropdownExpanded')
    }
  }, [location.pathname])

  const expandMetadataDropdown = () => {
    try {
      // Find the metadata dropdown in the sidebar
      // This targets the sidebar navigation item with "Metadata" text
      const metadataNavItem = Array.from(document.querySelectorAll('nav a, nav button'))
        .find(el => el.textContent?.includes('Metadata'))
      
      if (metadataNavItem) {
        // Look for the parent container that might have dropdown functionality
        let dropdownContainer = metadataNavItem.closest('[data-dropdown], .dropdown, .nav-item')
        
        if (!dropdownContainer) {
          // If no specific dropdown container, look for parent with children
          dropdownContainer = metadataNavItem.parentElement
        }
        
        if (dropdownContainer) {
          // Try to find and click the dropdown trigger if it's collapsed
          const isExpanded = dropdownContainer.querySelector('[aria-expanded="true"]') ||
                           dropdownContainer.classList.contains('expanded') ||
                           dropdownContainer.classList.contains('open')
          
          if (!isExpanded) {
            // Try to trigger the dropdown by clicking the metadata item
            metadataNavItem.click()
          }
          
          // Also add a CSS class to force expansion if needed
          dropdownContainer.classList.add('force-expanded')
        }
      }
    } catch (error) {
      console.warn('Could not auto-expand metadata dropdown:', error)
    }
  }

  // Return whether the dropdown should be expanded
  const shouldExpandMetadataDropdown = () => {
    const isOnMetadataRoute = location.pathname.includes('/metadata')
    const wasExpanded = localStorage.getItem('metadataDropdownExpanded') === 'true'
    return isOnMetadataRoute || wasExpanded
  }

  return {
    shouldExpandMetadataDropdown: shouldExpandMetadataDropdown(),
    expandMetadataDropdown
  }
}
