{"name": "aero-metadata-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky", "format": "prettier --write ."}, "dependencies": {"@bitcine/cinesend-theme": "^0.3.90", "@sentry/react": "^9.6.0", "@sentry/vite-plugin": "^3.2.2", "@tanstack/react-query": "^4.29.19", "@uppy/aws-s3": "^3.3.1", "@uppy/core": "^3.3.1", "@uppy/xhr-upload": "^3.3.1", "autoprefixer": "^10.4.20", "axios": "^1.4.0", "dayjs": "^1.11.9", "dayjs-plugin-utc": "^0.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-helmet": "^6.1.0", "react-router-dom": "^6.14.1", "react-tooltip": "^5.27.1", "tailwindcss": "^3.4.14", "uuid": "^11.0.3"}, "devDependencies": {"@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "vite": "5.4.9"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "prettier --write"}}